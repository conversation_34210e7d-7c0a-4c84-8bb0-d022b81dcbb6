import request, { AxiosInstance, AxiosRequestConfig } from 'axios';
import rax, { RetryConfig } from 'retry-axios';
import isRecord from './is-record';

export type Errors = string | {
    [key: string]: string
}

declare module 'retry-axios' {
    interface RetryConfig {
        retryDecider?: () => boolean;
    }
}
declare module 'axios' {
    interface AxiosError {
        errors: Errors;
    }

    interface AxiosRequestConfig {
        raxConfig?: RetryConfig;
    }
}

rax.attach();
request.defaults.raxConfig = {
    retryDelay: 2000,
    backoffType: 'static',
    shouldRetry: ({ config, response }) => {
        const raxConfig = config.raxConfig || {};

        if (raxConfig?.retryDecider && !raxConfig.retryDecider()) {
            return false;
        }

        if (!config.method || !raxConfig.httpMethodsToRetry?.includes(config.method.toUpperCase())) {
            return false;
        }

        return response?.status === 449;
    }
};
request.defaults.maxContentLength = Infinity;
request.defaults.maxBodyLength = Infinity;

request.interceptors.response.use(
    response => {
        return response;
    },
    e => {
        if (request.isAxiosError(e)) {
            if (e.response) {
                const { data, status } = e.response;
                if (status === 401) {
                    e.errors = 'Unauthorized';
                } else {
                    if (isRecord(data)) {
                        if (status === 422) {
                            e.errors = data;
                        } else if ('message' in data) {
                            e.errors = data['message'];
                        }
                    } else {
                        e.errors = data as string;
                    }
                }

                if (typeof e.errors !== 'string') {
                    e.message = Object.values(e.errors).join('\n');
                } else {
                    e.message = e.errors;
                }
            }
        }
        return Promise.reject(e);
    }
);

export default request;

export type RequestConfig = AxiosRequestConfig
export type RequestInstance = AxiosInstance
export const isRequestError = request.isAxiosError;
