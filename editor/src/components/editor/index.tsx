import { forwardRef, memo, ReactNode, useCallback, useMemo, useRef, useState } from 'react';
import { debounce } from 'lodash';
import File from '../../entities/file';
import { Buffer } from 'buffer';
import ModeProvider, { useMode } from './mode-provider';
import ToolBar from './tool-bar';
import Document from './editable/document';
import Raw from './editable/raw';
import Plate from './plate';
import { Descendant, Editor as SlateEditor } from 'slate';
import useFormatMessage from '../../lib/use-format-message';
import Empty from '../empty';
import Button from '../button';
import { VscError } from 'react-icons/vsc';
import EditorTour from '../tour/editor-tour';
import HoverBar from './hover-bar';

interface EditorProps {
    file: File;
    onChange: (buf: Buffer) => void;
}

const Editor = memo(forwardRef<SlateEditor, EditorProps>(({ file, onChange }, ref) => {
    const { isDocument, serializer, plugins, withEditor, toggle } = useMode();
    const t = useFormatMessage();

    const [content, setContent] = useState(() => file.content.toString());

    const initialValue = useMemo<Descendant[] | false>(() => {
        try {
            return serializer.deserialize(content);
        } catch (e) {
            console.log(e);
            return false;
        }
    }, []);

    const onContentChange = useCallback(debounce((value: Descendant[]) => {
        setContent((prevContent) => {
            const nextContent = serializer.serialize(value);
            if (nextContent !== prevContent) {
                onChange(Buffer.from(nextContent));
            }
            return nextContent;
        });
    }, 500), [serializer, onChange]);

    const valueRef = useRef(initialValue);

    const handleChange = useCallback((value: Descendant[]) => {
        if (valueRef.current !== value) {
            onContentChange(value);
            valueRef.current = value;
        }
    }, [onContentChange]);

    const renderEditable = useCallback((editable: ReactNode) => {
        return isDocument ? <Document editable={editable} /> : <Raw editable={editable} />;
    }, [isDocument]);

    if (initialValue === false) {
        return <Empty message={t('editor.markdown.error')} icon={<VscError />}>
            <Button onClick={toggle} variant={'light'}>{t('editor.markdown.switch')}</Button>
        </Empty>;
    }

    return <Plate
        ref={ref}
        initialValue={initialValue}
        plugins={plugins}
        withEditor={withEditor}
        onChange={handleChange}
        renderEditable={renderEditable}
        placeholder={t('editor.placeholder')}
    >
        <ToolBar />
        <HoverBar />
    </Plate>;
}), () => {
    return true;
});

const ModeEditor = forwardRef<SlateEditor, EditorProps>((props, ref) => {
    return <EditorTour>
        <ModeProvider file={props.file}>
            <Editor {...props} ref={ref} />
        </ModeProvider>
    </EditorTour>;
});

export default ModeEditor;
