import { ReactNode } from 'react';
import { styled } from '@topwrite/common';

interface Props {
    editable: ReactNode;
}

export default function Document({ editable }: Props) {
    return <Container>
        {editable}
    </Container>;
}

const Container = styled.div`
    flex: auto;
    overflow: hidden;
    position: relative;
    background: var(--ttw-priview-background);
    overflow-y: auto;
    padding: 0 16px;

    [data-slate-editor] {
        margin: 20px auto;
        max-width: 900px;
        border: 1px solid var(--ttw-border-color);
        background: var(--ttw-editor-background);
        color: var(--ttw-color);
        position: relative;
        word-wrap: break-word;
        outline: 0;
        line-height: 1.7;
        font-size: 16px;
        padding: 30px 30px calc(100vh - 168px);

        [data-placeholder] {
            position: relative;
        }

        [data-placeholder]::before {
            position: absolute;
            content: attr(data-placeholder);
            color: #D8DAD9;
            user-select: none;
            pointer-events: none;
            white-space: nowrap;
            top: 50%;
            transform: translateY(-50%);
        }
    }
`;
