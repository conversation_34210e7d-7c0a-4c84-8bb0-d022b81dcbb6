import { InjectedComponentSet, styled, useModel, useSelector, useThemeContext } from '@topwrite/common';
import { VscCircleFilled, VscPreview, VscSourceControl, VscSync } from 'react-icons/vsc';
import { BsSun, BsMoon, BsCircleHalf } from 'react-icons/bs';
import Tooltip from '../components/tooltip';
import { ReactComponent as Feedback } from '../images/feedback.svg';
import { ReactNode, useCallback, useEffect, useState } from 'react';
import { socket } from '../lib/socket';
import Toast from '../components/toast';

export interface MenuItemProps {
    tooltip: string;
    children: ReactNode;
    onClick: string | (() => void);
}

const MenuItem = ({ tooltip, children, onClick, ...props }: MenuItemProps) => {
    if (typeof onClick === 'string') {
        const href = onClick;
        onClick = () => {
            window.open(href);
        };
    }

    return <Tooltip placement='left' tooltip={tooltip}>
        <Action onClick={onClick} {...props}>{children}</Action>
    </Tooltip>;
};

export default function StatusBar() {

    const [{ status: { branch }, syncing, current }, { sync }] = useModel('workspace');
    const { preview, feedback } = useSelector('options');
    const [message, setMessage] = useState<string>('');
    const { isDarkTheme, isAutoTheme, toggleTheme } = useThemeContext();

    let ab: ReactNode = null;
    if (branch.ab[0] + branch.ab[1] > 0) {
        ab = <span>{branch.ab[1]}↓ {branch.ab[0]}↑</span>;
    }

    useEffect(() => {
        const listener = (error: string) => {
            Toast.error(error, { closable: true });
        };

        const event = `sync.error`;
        socket.on(event, listener);
        return () => {
            socket.off(event, listener);
        };
    }, []);

    useEffect(() => {
        const listener = (message: string) => {
            setMessage(message);
        };

        const event = `sync.message`;
        socket.on(event, listener);
        return () => {
            socket.off(event, listener);
        };
    }, []);

    const clickHandler = useCallback(() => {
        if (!syncing) {
            sync();
        }
    }, [syncing]);

    let themeItem: ReactNode;
    if (isAutoTheme) {
        themeItem = <MenuItem onClick={toggleTheme} tooltip={'theme.auto'}><BsCircleHalf /></MenuItem>;
    } else if (isDarkTheme) {
        themeItem = <MenuItem onClick={toggleTheme} tooltip={'theme.dark'}><BsMoon /></MenuItem>;
    } else {
        themeItem = <MenuItem onClick={toggleTheme} tooltip={'theme.light'}><BsSun /></MenuItem>;
    }

    return <Container>
        <LeftContainer>
            <Item><VscCircleFilled color='var(--bs-success)' size={20} /></Item>
            <Item><VscSourceControl /><span>{branch.head}</span></Item>
            <Tooltip placement='top' tooltip={'status-bar.sync'}>
                <Item onClick={clickHandler}><VscSync className={syncing ? 'bi-spin' : ''} />{ab}</Item>
            </Tooltip>
            {syncing && <MessageItem>{message}</MessageItem>}
        </LeftContainer>
        <RightContainer>
            <Item>{current}</Item>
        </RightContainer>
        <Actions>
            {themeItem}
            {preview &&
                <MenuItem onClick={preview} tooltip={'status-bar.preview'} data-tour={'editor-preview'}><VscPreview /></MenuItem>}
            {feedback && <MenuItem onClick={feedback} tooltip={'status-bar.feedback'}><Feedback /></MenuItem>}
            <InjectedComponentSet role={'editor:menu:item'} component={MenuItem} />
        </Actions>
    </Container>;
}

const Item = styled.div`
    line-height: 24px;
    padding: 0 5px;
    display: flex;
    align-items: center;
    cursor: pointer;

    .bi:not(:only-child) {
        margin-right: 4px;
    }

    &:hover {
        background: var(--ttw-box-hover-background);
    }

    span {
        font-family: Segoe WPC, Segoe UI, Microsoft YaHei, sans-serif;
        font-size: 12px;
    }
`;

const MessageItem = styled(Item)`
    cursor: default;
    color: #6a737d;

    &:hover {
        background: inherit;
    }
`;

const LeftContainer = styled.div`
    display: flex;
`;

const RightContainer = styled.div`

`;

const Container = styled.div`
    order: 10;
    height: 25px;
    border-top: 1px solid var(--ttw-border-color);
    background: var(--ttw-foreground);
    font-size: 13px;
    position: relative;
    display: flex;
    justify-content: space-between;

    body:fullscreen & {
        display: none;
    }
`;

const Action = styled.span`
    width: 32px;
    height: 32px;
    background: var(--ttw-box-background);
    box-shadow: 0 3px 6px rgb(0 0 0 / 25%);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    margin-bottom: 10px;
    color: var(--ttw-color);

    svg {
        width: 18px;
        height: 18px;
    }

    &:hover {
        background: var(--ttw-box-hover-background);
    }
`;

const Actions = styled.div`
    position: fixed;
    right: 16px;
    bottom: 25px;
    z-index: 400;
    display: flex;
    flex-direction: column-reverse;
`;
